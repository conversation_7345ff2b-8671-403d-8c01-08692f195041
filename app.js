const express = require('express');
const fileUpload = require('express-fileupload');
const { exec } = require('child_process');
const path = require('path');
const fs = require('fs');
const os = require('os');
const util = require('util');
const stream = require('stream');
const archiver = require('archiver');

const app = express();
const port = 3000;
const archiveDir = path.join(__dirname, 'archives');
const uploadDir = path.join(__dirname, 'uploads');
const extractDir = path.join(__dirname, 'extracted');
const encodedDir = path.join(__dirname, 'encoded');

// Ensure our directories exist
[archiveDir, uploadDir, extractDir, encodedDir].forEach(dir => {
  if (!fs.existsSync(dir)) {
    fs.mkdirSync(dir, { recursive: true });
  }
});



// Promisify exec for easier async usage
const execPromise = util.promisify(exec);
const fsPromises = fs.promises;
const pipeline = util.promisify(stream.pipeline);

app.use(express.static(__dirname));

// Middleware
app.use(express.json({ limit: '10gb' })); // Increase JSON payload limit
app.use(express.urlencoded({ limit: '10gb', extended: true }));
app.use(fileUpload({
  createParentPath: true,
  useTempFiles: true,
  tempFileDir: os.tmpdir(),// Dedicated temp directory
  abortOnLimit: true,
  limits: { fileSize: 10 * 1024 * 1024 * 1024 }, // 10GB limit
  responseOnLimit: 'File size exceeds 10GB limit',
}));


const zmtPath = '/home/<USER>/Desktop/zmt_compression/zmt';
const zmtdcmPath = './zmtdcm'; // Path to the DICOM-specific compression tool
const videoScriptPath = '/home/<USER>/Desktop/video_bash/compressmp4';
const videoBashDir = '/home/<USER>/Desktop/video_bash/';

// Check if we're on macOS and need to use Docker
const isMacOS = os.platform() === 'darwin';
const useDocker = isMacOS;

// Chunked upload session storage
const uploadSessions = {};

// Helper function to check if a file is a DICOM file
function isDicomFile(fileName) {
  const lowerFileName = fileName.toLowerCase();
  const dicomExtensions = ['.dcm', '.dicom', '.dic', '.ima', '.img'];
  return dicomExtensions.some(ext => lowerFileName.endsWith(ext));
}

// Helper function to compress DICOM files using zmtdcm
async function compressDicomFile(inputPath, outputDir) {
  try {
    const fileName = path.basename(inputPath);
    const nameWithoutExt = path.parse(fileName).name;
    const outputFileName = `${nameWithoutExt}_compressed.dcm`;
    const outputPath = path.join(outputDir, outputFileName);

    console.log(`Compressing DICOM file: ${inputPath} -> ${outputPath}`);

    // Ensure output directory exists
    await fsPromises.mkdir(outputDir, { recursive: true });

    // Run zmtdcm compression with correct syntax: zmtdcm input.dcm output.dcm
    const { stdout, stderr } = await execPromise(`${zmtdcmPath} "${inputPath}" "${outputPath}"`, {
      timeout: 5 * 60 * 1000 // 5 minutes timeout
    });

    console.log('DICOM compression stdout:', stdout);
    if (stderr) {
      console.error('DICOM compression stderr:', stderr);
    }

    // Check if the compressed file was created
    if (!fs.existsSync(outputPath)) {
      throw new Error(`Compressed DICOM file not found at expected location: ${outputPath}`);
    }

    // Verify the compressed file has content
    const compressedStats = await fsPromises.stat(outputPath);
    if (compressedStats.size === 0) {
      throw new Error('Compressed DICOM file has zero size');
    }

    console.log(`DICOM compression successful: ${fileName} (${compressedStats.size} bytes)`);
    return outputPath;
  } catch (error) {
    console.error('Error compressing DICOM file:', error);
    throw new Error(`Failed to compress DICOM file: ${error.message}`);
  }
}

// Endpoint for handling chunked uploads
app.post('/api/upload-chunk', async (req, res) => {
  try {
    if (!req.files || !req.files.chunk) {
      return res.status(400).send('No chunk was uploaded.');
    }

    const {
      sessionId,
      chunkIndex,
      totalChunks,
      fileName,
      relativePath,
      fileSize,
      totalFiles,
      archiveName
    } = req.body;

    // Check for restricted file types (images and videos)
    const restrictedExtensions = [
      // Images
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg',
      // Videos
      '.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg'
    ];

    // Allowed special file types (these override the restricted extensions)
    const allowedSpecialTypes = [
      // DICOM files
      '.dcm', '.dicom', '.dic', '.ima', '.img'
    ];

    // Check if file has a restricted extension
    const lowerFileName = fileName.toLowerCase();

    // First check if it's an allowed special type (like DICOM)
    const isAllowedSpecialType = allowedSpecialTypes.some(ext => lowerFileName.endsWith(ext));

    // If it's an allowed special type, we can proceed
    if (isAllowedSpecialType) {
      console.log(`Allowing special file type: ${fileName}`);
    } else {
      // Otherwise, check if it has a restricted extension
      const hasRestrictedExt = restrictedExtensions.some(ext => lowerFileName.endsWith(ext));

      // Reject if it has a restricted extension
      if (hasRestrictedExt) {
        return res.status(400).json({
          success: false,
          message: `File type not allowed: ${fileName}. Image and video files are currently restricted. DICOM medical image files are allowed.`
        });
      }
    }

    // Initialize session if it doesn't exist
    if (!uploadSessions[sessionId]) {
      uploadSessions[sessionId] = {
        files: {},
        tempDir: path.join(uploadDir, `temp_${sessionId}`),
        archiveName: archiveName || `archive_${Date.now()}.zmt`,
        totalFiles: parseInt(totalFiles, 10) || 0,
        completedFiles: 0
      };

      // Create temp directory for this session
      await fsPromises.mkdir(uploadSessions[sessionId].tempDir, { recursive: true });
    }

    const session = uploadSessions[sessionId];

    // Initialize file tracking if it's a new file
    if (!session.files[relativePath]) {
      session.files[relativePath] = {
        chunks: {},
        totalChunks: parseInt(totalChunks, 10),
        receivedChunks: 0,
        fileSize: parseInt(fileSize, 10)
      };
    }

    const fileInfo = session.files[relativePath];
    const chunkData = req.files.chunk;
    const chunkDir = path.join(session.tempDir, '.chunks', relativePath);

    // Create directory for chunks if it doesn't exist
    await fsPromises.mkdir(chunkDir, { recursive: true });

    // Save the chunk
    const chunkPath = path.join(chunkDir, `chunk_${chunkIndex}`);
    await chunkData.mv(chunkPath);

    // Update tracking
    fileInfo.chunks[chunkIndex] = chunkPath;
    fileInfo.receivedChunks++;

    // Check if all chunks for this file have been received
    if (fileInfo.receivedChunks === fileInfo.totalChunks) {
      // All chunks received, reassemble the file
      const fileDir = path.dirname(path.join(session.tempDir, relativePath));

      // Create directory structure if needed
      if (!fs.existsSync(fileDir)) {
        await fsPromises.mkdir(fileDir, { recursive: true });
      }

      // Create the final file
      const finalFilePath = path.join(session.tempDir, relativePath);
      const writeStream = fs.createWriteStream(finalFilePath);

      // Write chunks in order
      for (let i = 0; i < fileInfo.totalChunks; i++) {
        const chunkPath = fileInfo.chunks[i];
        const chunkData = await fsPromises.readFile(chunkPath);
        writeStream.write(chunkData);
      }

      // Close the write stream
      await new Promise((resolve, reject) => {
        writeStream.on('finish', resolve);
        writeStream.on('error', reject);
        writeStream.end();
      });

      // Clean up chunks
      for (let i = 0; i < fileInfo.totalChunks; i++) {
        await fsPromises.unlink(fileInfo.chunks[i]);
      }

      // Update completed files count
      session.completedFiles++;

      console.log(`File ${relativePath} reassembled (${fileInfo.receivedChunks}/${fileInfo.totalChunks} chunks)`);
    }

    res.json({
      success: true,
      message: 'Chunk uploaded successfully',
      progress: {
        file: {
          receivedChunks: fileInfo.receivedChunks,
          totalChunks: fileInfo.totalChunks,
          complete: fileInfo.receivedChunks === fileInfo.totalChunks
        },
        session: {
          completedFiles: session.completedFiles,
          totalFiles: session.totalFiles,
          complete: session.completedFiles === session.totalFiles
        }
      }
    });
  } catch (error) {
    console.error('Chunk upload error:', error);
    res.status(500).send(`Error during chunk upload: ${error.message}`);
  }
});

// Endpoint to finalize upload and create archive
app.post('/api/finalize-upload', async (req, res) => {
  try {
    const { sessionId, archiveName, hasFolder } = req.body;

    if (!sessionId || !uploadSessions[sessionId]) {
      return res.status(400).send('Invalid or expired session ID');
    }

    const session = uploadSessions[sessionId];
    const tempDir = session.tempDir;

    // Ensure the archive name has the .zmt extension
    let finalArchiveName = archiveName || session.archiveName;
    if (!finalArchiveName.toLowerCase().endsWith('.zmt')) {
      finalArchiveName += '.zmt';
    }

    const archivePath = path.join(archiveDir, finalArchiveName);

    // Check if all files have been uploaded
    if (session.completedFiles < session.totalFiles) {
      return res.status(400).json({
        success: false,
        message: `Not all files have been uploaded (${session.completedFiles}/${session.totalFiles})`
      });
    }

    try {
      // Clean up any remaining chunk directories
      const chunksDir = path.join(tempDir, '.chunks');
      if (fs.existsSync(chunksDir)) {
        await deleteDirectory(chunksDir);
      }

      // Check for DICOM files and handle them separately
      console.log('Checking for DICOM files to compress...');
      const dicomFiles = [];
      const nonDicomFiles = [];

      // Recursively find all files and categorize them
      async function categorizeFiles(dir) {
        const entries = await fsPromises.readdir(dir, { withFileTypes: true });
        for (const entry of entries) {
          const fullPath = path.join(dir, entry.name);
          if (entry.isDirectory()) {
            await categorizeFiles(fullPath);
          } else if (isDicomFile(entry.name)) {
            dicomFiles.push(fullPath);
          } else {
            nonDicomFiles.push(fullPath);
          }
        }
      }

      await categorizeFiles(tempDir);

      let processedFiles = [];

      // Handle DICOM files - compress and store as individual .dcm files
      if (dicomFiles.length > 0) {
        console.log(`Found ${dicomFiles.length} DICOM files to compress`);

        for (const dicomFilePath of dicomFiles) {
          try {
            console.log(`Compressing DICOM file: ${dicomFilePath}`);
            const compressedPath = await compressDicomFile(dicomFilePath, path.dirname(dicomFilePath));

            // Move compressed file to archives directory with proper name
            const relativePath = path.relative(tempDir, compressedPath);
            const finalPath = path.join(archiveDir, relativePath);

            // Ensure directory exists
            await fsPromises.mkdir(path.dirname(finalPath), { recursive: true });
            await fsPromises.copyFile(compressedPath, finalPath);

            processedFiles.push({
              type: 'dicom',
              originalName: path.basename(dicomFilePath),
              compressedName: path.basename(compressedPath),
              path: relativePath
            });

            console.log(`DICOM file processed: ${path.basename(dicomFilePath)} -> ${path.basename(compressedPath)}`);
          } catch (compressionError) {
            console.error(`Failed to compress DICOM file ${dicomFilePath}:`, compressionError);
            // Continue with other files even if one fails
          }
        }
      }

      // Handle non-DICOM files - create ZMT archive only if there are non-DICOM files
      if (nonDicomFiles.length > 0) {
        console.log(`Found ${nonDicomFiles.length} non-DICOM files to archive`);

        // Create a temporary directory with only non-DICOM files
        const nonDicomTempDir = path.join(uploadDir, `temp_nondicom_${Date.now()}`);
        await fsPromises.mkdir(nonDicomTempDir, { recursive: true });

        // Copy non-DICOM files to the temporary directory maintaining structure
        for (const filePath of nonDicomFiles) {
          const relativePath = path.relative(tempDir, filePath);
          const destPath = path.join(nonDicomTempDir, relativePath);

          // Ensure directory exists
          await fsPromises.mkdir(path.dirname(destPath), { recursive: true });
          await fsPromises.copyFile(filePath, destPath);
        }

        // Create ZMT archive for non-DICOM files
        console.log(`Starting archive creation for non-DICOM files...`);
        const { stdout, stderr } = await execPromise(`${zmtPath} a "${archivePath}" "${nonDicomTempDir}"`, {
          timeout: 30 * 60 * 1000 // 30 minutes timeout
        });

        console.log('Archive creation stdout:', stdout);
        if (stderr) {
          console.error('Archive creation stderr:', stderr);
        }

        // Verify the archive was created and has content
        const archiveStats = await fsPromises.stat(archivePath);
        if (archiveStats.size === 0) {
          throw new Error('Created archive has zero size. Archiving may not be complete.');
        }

        console.log(`Archive created successfully: ${archivePath} (${archiveStats.size} bytes)`);

        // Clean up temporary non-DICOM directory
        await deleteDirectory(nonDicomTempDir);

        processedFiles.push({
          type: 'archive',
          archiveName: finalArchiveName,
          fileCount: nonDicomFiles.length
        });
      }

      // Clean up the session
      delete uploadSessions[sessionId];

      // Prepare response based on what was processed
      let responseMessage = 'Files processed successfully';
      let responseData = { success: true, message: responseMessage };

      if (dicomFiles.length > 0 && nonDicomFiles.length > 0) {
        responseMessage = `Processed ${dicomFiles.length} DICOM files and archived ${nonDicomFiles.length} other files`;
        responseData.dicomFiles = processedFiles.filter(f => f.type === 'dicom');
        responseData.archiveName = finalArchiveName;
      } else if (dicomFiles.length > 0) {
        responseMessage = `Processed ${dicomFiles.length} DICOM files`;
        responseData.dicomFiles = processedFiles.filter(f => f.type === 'dicom');
      } else {
        responseMessage = 'Files archived successfully';
        responseData.archiveName = finalArchiveName;
      }

      responseData.message = responseMessage;
      res.json(responseData);
    } finally {
      // Clean up the temp directory regardless of success or failure
      if (fs.existsSync(tempDir)) {
        console.log(`Cleaning up temporary directory: ${tempDir}`);
        await deleteDirectory(tempDir);
        console.log('Temporary directory cleanup complete');
      }
    }
  } catch (error) {
    console.error('Finalize upload error:', error);
    res.status(500).json({
      success: false,
      message: `Error during finalization: ${error.message}`
    });
  }
});

// Original archive endpoint (kept for backward compatibility)
app.post('/api/archive', async (req, res) => {
  try {
    if (!req.files) {
      return res.status(400).send('No files were uploaded.');
    }

    // Check for restricted file types (images and videos)
    const restrictedExtensions = [
      // Images
      '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg',
      // Videos
      '.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg'
    ];

    // Allowed special file types (these override the restricted extensions)
    const allowedSpecialTypes = [
      // DICOM files
      '.dcm', '.dicom', '.dic', '.ima', '.img'
    ];

    // Function to check if a file has a restricted extension
    const hasRestrictedExtension = (fileName) => {
      const lowerFileName = fileName.toLowerCase();

      // First check if it's an allowed special type
      const isAllowedSpecial = allowedSpecialTypes.some(ext => lowerFileName.endsWith(ext));
      if (isAllowedSpecial) {
        return false; // Special types are always allowed
      }

      // Then check if it has a restricted extension
      const isRestricted = restrictedExtensions.some(ext => lowerFileName.endsWith(ext));
      return isRestricted;
    };

    // Check uploaded files for restricted types
    let restrictedFiles = [];

    if (req.files.file) {
      const file = req.files.file;
      if (Array.isArray(file)) {
        file.forEach(f => {
          if (hasRestrictedExtension(f.name)) {
            restrictedFiles.push(f.name);
          }
        });
      } else if (hasRestrictedExtension(file.name)) {
        restrictedFiles.push(file.name);
      }
    }

    if (req.files.files) {
      const files = req.files.files;
      if (Array.isArray(files)) {
        files.forEach(f => {
          if (hasRestrictedExtension(f.name)) {
            restrictedFiles.push(f.name);
          }
        });
      } else if (hasRestrictedExtension(files.name)) {
        restrictedFiles.push(files.name);
      }
    }

    if (restrictedFiles.length > 0) {
      return res.status(400).json({
        success: false,
        message: `File type(s) not allowed. Image and video files are currently restricted. DICOM medical image files (.dcm, .dicom, etc.) are allowed.`,
        restrictedFiles: restrictedFiles
      });
    }

    const archiveName = req.body.archiveName || `archive_${Date.now()}.zmt`;
    const archivePath = path.join(archiveDir, archiveName);

    // Determine if we're dealing with a single file or multiple files/folder
    if (req.files.file && !req.files.files) {
      // Single file upload (backward compatibility)
      const uploadedFile = req.files.file;
      const uploadPath = path.join(uploadDir, uploadedFile.name);

      // Save the file directly to uploads directory
      await uploadedFile.mv(uploadPath);

      // Check if it's a DICOM file and handle it differently
      if (isDicomFile(uploadedFile.name)) {
        try {
          console.log(`Compressing single DICOM file: ${uploadPath}`);
          const compressedPath = await compressDicomFile(uploadPath, uploadDir);

          // Move compressed file to archives directory instead of creating ZMT archive
          const compressedFileName = path.basename(compressedPath);
          const finalPath = path.join(archiveDir, compressedFileName);
          await fsPromises.copyFile(compressedPath, finalPath);

          // Clean up temporary files
          await fsPromises.unlink(uploadPath);
          await fsPromises.unlink(compressedPath);

          console.log(`DICOM file processed: ${uploadedFile.name} -> ${compressedFileName}`);

          res.json({
            success: true,
            message: 'DICOM file compressed successfully',
            fileName: compressedFileName,
            type: 'dicom'
          });
          return;
        } catch (compressionError) {
          console.error(`Failed to compress DICOM file ${uploadPath}:`, compressionError);
          // Fall back to regular archiving if compression fails
        }
      }

      // Create archive directly from the file (for non-DICOM files)
      await execPromise(`${zmtPath} a "${archivePath}" "${uploadPath}"`);

      // Clean up the uploaded file
      await fsPromises.unlink(uploadPath);
    } else {
      // Multiple files or folder upload
      const tempDir = path.join(uploadDir, `temp_${Date.now()}`);

      try {
        // Create temp directory
        await fsPromises.mkdir(tempDir, { recursive: true });

        // Get all files (either from files array or single file)
        const uploadedFiles = req.files.files
          ? (Array.isArray(req.files.files) ? req.files.files : [req.files.files])
          : [req.files.file];

        // Save all files to temp directory preserving structure
        for (const file of uploadedFiles) {
          // Get the relative path (for directory structure)
          let relativePath = file.name;

          // If the file has a path (from a directory upload), use it
          if (file.name.includes('/') || file.name.includes('\\')) {
            relativePath = file.name;
          }

          // Create the full path for the file
          const uploadPath = path.join(tempDir, relativePath);

          // Create directory structure if needed
          const dirPath = path.dirname(uploadPath);
          if (!fs.existsSync(dirPath)) {
            await fsPromises.mkdir(dirPath, { recursive: true });
          }

          // Save the file
          await file.mv(uploadPath);
        }

        // Check for DICOM files and handle them separately
        console.log('Checking for DICOM files to compress...');
        const dicomFiles = [];
        const nonDicomFiles = [];

        // Recursively find all files and categorize them
        async function categorizeFiles(dir) {
          const entries = await fsPromises.readdir(dir, { withFileTypes: true });
          for (const entry of entries) {
            const fullPath = path.join(dir, entry.name);
            if (entry.isDirectory()) {
              await categorizeFiles(fullPath);
            } else if (isDicomFile(entry.name)) {
              dicomFiles.push(fullPath);
            } else {
              nonDicomFiles.push(fullPath);
            }
          }
        }

        await categorizeFiles(tempDir);

        let processedFiles = [];

        // Handle DICOM files - compress and store as individual .dcm files
        if (dicomFiles.length > 0) {
          console.log(`Found ${dicomFiles.length} DICOM files to compress`);

          for (const dicomFilePath of dicomFiles) {
            try {
              console.log(`Compressing DICOM file: ${dicomFilePath}`);
              const compressedPath = await compressDicomFile(dicomFilePath, path.dirname(dicomFilePath));

              // Move compressed file to archives directory with proper name
              const relativePath = path.relative(tempDir, compressedPath);
              const finalPath = path.join(archiveDir, relativePath);

              // Ensure directory exists
              await fsPromises.mkdir(path.dirname(finalPath), { recursive: true });
              await fsPromises.copyFile(compressedPath, finalPath);

              processedFiles.push({
                type: 'dicom',
                originalName: path.basename(dicomFilePath),
                compressedName: path.basename(compressedPath),
                path: relativePath
              });

              console.log(`DICOM file processed: ${path.basename(dicomFilePath)} -> ${path.basename(compressedPath)}`);
            } catch (compressionError) {
              console.error(`Failed to compress DICOM file ${dicomFilePath}:`, compressionError);
              // Continue with other files even if one fails
            }
          }
        }

        // Handle non-DICOM files - create ZMT archive only if there are non-DICOM files
        if (nonDicomFiles.length > 0) {
          console.log(`Found ${nonDicomFiles.length} non-DICOM files to archive`);

          // Create a temporary directory with only non-DICOM files
          const nonDicomTempDir = path.join(uploadDir, `temp_nondicom_${Date.now()}`);
          await fsPromises.mkdir(nonDicomTempDir, { recursive: true });

          // Copy non-DICOM files to the temporary directory maintaining structure
          for (const filePath of nonDicomFiles) {
            const relativePath = path.relative(tempDir, filePath);
            const destPath = path.join(nonDicomTempDir, relativePath);

            // Ensure directory exists
            await fsPromises.mkdir(path.dirname(destPath), { recursive: true });
            await fsPromises.copyFile(filePath, destPath);
          }

          // Create ZMT archive for non-DICOM files
          try {
            console.log(`Starting archive creation for non-DICOM files...`);
            const { stdout, stderr } = await execPromise(`${zmtPath} a "${archivePath}" "${nonDicomTempDir}"`, {
              timeout: 30 * 60 * 1000 // 30 minutes timeout
            });

            console.log('Archive creation stdout:', stdout);
            if (stderr) {
              console.error('Archive creation stderr:', stderr);
            }

            // Verify the archive was created and has content
            const archiveStats = await fsPromises.stat(archivePath);
            if (archiveStats.size === 0) {
              throw new Error('Created archive has zero size. Archiving may not be complete.');
            }

            console.log(`Archive created successfully: ${archivePath} (${archiveStats.size} bytes)`);

            processedFiles.push({
              type: 'archive',
              archiveName: archiveName,
              fileCount: nonDicomFiles.length
            });
          } catch (archiveError) {
            console.error('Error during archive creation:', archiveError);
            throw new Error(`Failed to create archive: ${archiveError.message}`);
          } finally {
            // Clean up temporary non-DICOM directory
            await deleteDirectory(nonDicomTempDir);
          }
        }

        // Prepare response based on what was processed
        let responseMessage = 'Files processed successfully';
        let responseData = { success: true, message: responseMessage };

        if (dicomFiles.length > 0 && nonDicomFiles.length > 0) {
          responseMessage = `Processed ${dicomFiles.length} DICOM files and archived ${nonDicomFiles.length} other files`;
          responseData.dicomFiles = processedFiles.filter(f => f.type === 'dicom');
          responseData.archiveName = archiveName;
        } else if (dicomFiles.length > 0) {
          responseMessage = `Processed ${dicomFiles.length} DICOM files`;
          responseData.dicomFiles = processedFiles.filter(f => f.type === 'dicom');
        } else {
          responseMessage = 'Files archived successfully';
          responseData.archiveName = archiveName;
        }

        responseData.message = responseMessage;
        res.json(responseData);
      } finally {
        // Clean up the temp directory regardless of success or failure
        if (fs.existsSync(tempDir)) {
          console.log(`Cleaning up temporary directory: ${tempDir}`);
          await deleteDirectory(tempDir);
          console.log('Temporary directory cleanup complete');
        }
      }
    }

    // This should only be reached for single file uploads that aren't DICOM
    res.json({
      success: true,
      message: 'Files archived successfully',
      archiveName: archiveName
    });
  } catch (error) {
    console.error('Archive error:', error);
    res.status(500).send(`Error during archiving: ${error.message}`);
  }
});

// Endpoint 2: List all archive files (both .zmt and .dcm)
app.get('/api/archives', (req, res) => {
  try {
    const files = fs.readdirSync(archiveDir)
      .filter(file => file.endsWith('.zmt') || file.endsWith('.dcm'))
      .map(file => {
        const filePath = path.join(archiveDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          created: stats.birthtime,
          type: file.endsWith('.dcm') ? 'dicom' : 'archive'
        };
      });

    res.json({
      success: true,
      archives: files
    });
  } catch (error) {
    console.error('List archives error:', error);
    res.status(500).send(`Error listing archives: ${error.message}`);
  }
});

// Endpoint 3: Unarchive a file
app.post('/api/unarchive', async (req, res) => {
  try {
    const { archiveName } = req.body;

    if (!archiveName) {
      return res.status(400).send('Archive name is required');
    }

    const archivePath = path.join(archiveDir, archiveName);

    // Check if archive exists
    if (!fs.existsSync(archivePath)) {
      return res.status(404).send('Archive not found');
    }

    // Create a unique extraction directory
    const extractionId = Date.now().toString();
    const extractionPath = path.join(extractDir, extractionId);
    fs.mkdirSync(extractionPath, { recursive: true });

    // Extract the archive - this will extract to uploads directory
    console.log(`Starting extraction of archive: ${archivePath}`);
    let extractionTime = 0;
    let isSingleFile = false;
    let singleFileName = '';

    try {
      // Record start time
      const startTime = Date.now();

      // Set a longer timeout for large archives (30 minutes)
      const { stdout, stderr } = await execPromise(`${zmtPath} x "${archivePath}"`, {
        timeout: 30 * 60 * 1000 // 30 minutes timeout
      });

      // Calculate extraction time in seconds
      extractionTime = (Date.now() - startTime) / 1000;

      console.log('Extraction stdout:', stdout);

      // Check if it's a single file extraction by parsing the output
      const fileMatch = stdout.match(/> (.+)/);
      if (fileMatch && fileMatch[1]) {
        const extractedPath = fileMatch[1];
        singleFileName = path.basename(extractedPath);

        // Check if the file exists in the uploads directory
        const uploadedEntries = await fsPromises.readdir(uploadDir, { withFileTypes: true });
        const uploadedFiles = uploadedEntries.filter(entry => entry.isFile());
        const uploadedDirs = uploadedEntries.filter(entry => entry.isDirectory());

        if (uploadedFiles.length === 1 && uploadedDirs.length === 0) {
          isSingleFile = true;
          console.log(`Detected single file extraction: ${singleFileName}`);
        }
      }

      // Log any stderr output
      if (stderr) {
        console.error('Extraction stderr:', stderr);
      }

      console.log(`Archive extraction completed successfully in ${extractionTime.toFixed(2)} seconds`);
    } catch (extractError) {
      console.error('Error during archive extraction:', extractError);
      throw new Error(`Failed to extract archive: ${extractError.message}`);
    }

    // Helper function to recursively move files and directories
    async function moveRecursively(sourcePath, destinationPath) {
      // Create destination directory if it doesn't exist
      if (!fs.existsSync(destinationPath)) {
        await fsPromises.mkdir(destinationPath, { recursive: true });
      }

      // Get all entries in the source directory
      const entries = await fsPromises.readdir(sourcePath, { withFileTypes: true });

      // Process each entry
      for (const entry of entries) {
        const srcPath = path.join(sourcePath, entry.name);
        const destPath = path.join(destinationPath, entry.name);

        if (entry.isDirectory()) {
          // Recursively process subdirectory
          await moveRecursively(srcPath, destPath);
          // Remove the empty source directory after all contents have been moved
          await fsPromises.rmdir(srcPath);
        } else {
          // Copy file then delete the original
          await fsPromises.copyFile(srcPath, destPath);
          await fsPromises.unlink(srcPath);
        }
      }
    }

    // Now we need to move files from uploads to our extraction directory
    try {
      console.log(`Moving extracted files from ${uploadDir} to ${extractionPath}`);

      // Check if there's only a single file in the uploads directory
      const uploadedEntries = await fsPromises.readdir(uploadDir, { withFileTypes: true });
      const uploadedFiles = uploadedEntries.filter(entry => entry.isFile());
      const uploadedDirs = uploadedEntries.filter(entry => entry.isDirectory());

      // If there's exactly one file and no directories, handle it as a single file
      if (uploadedFiles.length === 1 && uploadedDirs.length === 0) {
        console.log('Detected single file extraction, handling specially');
        const singleFile = uploadedFiles[0];
        const srcPath = path.join(uploadDir, singleFile.name);
        const destPath = path.join(extractionPath, singleFile.name);

        // Copy the file directly to the extraction directory
        await fsPromises.copyFile(srcPath, destPath);
        await fsPromises.unlink(srcPath);
        console.log(`Single file moved successfully: ${singleFile.name}`);
      } else {
        // Handle multiple files or directories
        await moveRecursively(uploadDir, extractionPath);
        console.log('All files moved successfully');
      }
    } catch (moveError) {
      console.error('Error moving files:', moveError);
      return res.status(500).send(`Error moving extracted files: ${moveError.message}`);
    }

    // Get list of extracted files in the extraction directory
    const extractedFiles = fs.readdirSync(extractionPath);

    // Subtract 0.25 seconds from the displayed extraction time to account for display delay
    const adjustedExtractionTime = Math.max(0, extractionTime - 0.025);

    res.json({
      success: true,
      message: `Archive ${archiveName} extracted successfully in ${adjustedExtractionTime.toFixed(2)} seconds`,
      extractionId: extractionId,
      extractedFiles: extractedFiles,
      extractionTime: extractionTime, // Keep the original time in the data for reference
      isSingleFile: isSingleFile,
      singleFileName: isSingleFile ? singleFileName : null
    });
  } catch (error) {
    console.error('Unarchive error:', error);
    res.status(500).send(`Error during unarchiving: ${error.message}`);
  }
});

// Helper function to recursively delete a directory
async function deleteDirectory(dirPath) {
  try {
    const entries = await fsPromises.readdir(dirPath, { withFileTypes: true });

    // Delete all files and subdirectories
    for (const entry of entries) {
      const fullPath = path.join(dirPath, entry.name);
      if (entry.isDirectory()) {
        await deleteDirectory(fullPath);
      } else {
        await fsPromises.unlink(fullPath);
      }
    }

    // Delete the empty directory
    await fsPromises.rmdir(dirPath);
    return true;
  } catch (error) {
    console.error(`Error deleting directory ${dirPath}:`, error);
    return false;
  }
}

// UPDATED ENDPOINT: Delete an archive file and its extracted files
app.delete('/api/archives/:archiveName', async (req, res) => {
  try {
    const { archiveName } = req.params;

    if (!archiveName) {
      return res.status(400).send('Archive name is required');
    }

    const archivePath = path.join(archiveDir, archiveName);

    // Check if archive exists
    if (!fs.existsSync(archivePath)) {
      return res.status(404).send('Archive not found');
    }

    // Delete the archive file
    await fsPromises.unlink(archivePath);

    // Find and delete any extraction directories that might be associated with this archive
    try {
      // Get all extraction directories
      const extractionDirs = await fsPromises.readdir(extractDir);
      let deletedExtractDirs = 0;

      // Loop through each extraction directory
      for (const dirName of extractionDirs) {
        const extractionPath = path.join(extractDir, dirName);

        // Check if it's a directory
        const stats = await fsPromises.stat(extractionPath);
        if (stats.isDirectory()) {
          // Delete the extraction directory
          const deleted = await deleteDirectory(extractionPath);
          if (deleted) {
            deletedExtractDirs++;
          }
        }
      }

      res.json({
        success: true,
        message: `Archive '${archiveName}' deleted successfully`,
        extractedDirectoriesDeleted: deletedExtractDirs
      });
    } catch (extractError) {
      console.error('Error cleaning up extraction directories:', extractError);
      // Still return success since the archive was deleted
      res.json({
        success: true,
        message: `Archive '${archiveName}' deleted successfully, but failed to clean up some extraction directories`,
        error: extractError.message
      });
    }
  } catch (error) {
    console.error('Delete archive error:', error);
    res.status(500).send(`Error deleting archive: ${error.message}`);
  }
});

// Endpoint to check if a path is a directory or a file
app.get('/api/check-path-type/:extractionId/:filename', (req, res) => {
  try {
    const { extractionId, filename } = req.params;
    const filePath = path.join(extractDir, extractionId, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).json({ error: 'Path not found' });
    }

    // Check if it's a directory
    const stats = fs.statSync(filePath);
    const isDirectory = stats.isDirectory();

    // Special handling for temp directories with a single file
    let isSingleFileInTempDir = false;
    let singleFilePath = null;

    if (isDirectory && filename.startsWith('temp_')) {
      // This might be a temp directory with a single file
      try {
        const dirEntries = fs.readdirSync(filePath, { withFileTypes: true });
        const files = dirEntries.filter(entry => entry.isFile());
        const dirs = dirEntries.filter(entry => entry.isDirectory());

        if (files.length === 1 && dirs.length === 0) {
          // We have a single file in a temp directory
          isSingleFileInTempDir = true;
          singleFilePath = path.join(filePath, files[0].name);
          console.log(`Detected single file in temp directory: ${files[0].name}`);
        }
      } catch (err) {
        console.error('Error checking directory contents:', err);
      }
    }

    res.json({
      path: filename,
      isDirectory: isDirectory,
      isFile: !isDirectory,
      size: stats.size,
      isSingleFileInTempDir: isSingleFileInTempDir,
      singleFileName: isSingleFileInTempDir ? path.basename(singleFilePath) : null
    });
  } catch (error) {
    console.error('Path type check error:', error);
    res.status(500).json({ error: `Error checking path type: ${error.message}` });
  }
});

// Endpoint to download an extracted file
app.get('/api/download/:extractionId/:filename', (req, res) => {
  try {
    const { extractionId, filename } = req.params;
    const filePath = path.join(extractDir, extractionId, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).send('File not found');
    }

    // Check if it's a directory
    const stats = fs.statSync(filePath);
    if (stats.isDirectory()) {
      // For directories, redirect to the folder download endpoint
      return res.redirect(`/api/download-folder/${extractionId}/${filename}`);
    }

    // For regular files, download directly
    res.download(filePath);
  } catch (error) {
    console.error('Download error:', error);
    res.status(500).send(`Error downloading file: ${error.message}`);
  }
});

// Endpoint to download a folder as a zip archive
app.get('/api/download-folder/:extractionId/:folderName', async (req, res) => {
  try {
    const { extractionId, folderName } = req.params;
    const folderPath = path.join(extractDir, extractionId, folderName);

    if (!fs.existsSync(folderPath)) {
      return res.status(404).send('Folder not found');
    }

    // Check if it's actually a directory
    const stats = fs.statSync(folderPath);
    if (!stats.isDirectory()) {
      // If it's a file, redirect to the file download endpoint
      return res.redirect(`/api/download/${extractionId}/${folderName}`);
    }

    // Special handling for temp directories with a single file
    if (folderName.startsWith('temp_')) {
      try {
        const dirEntries = fs.readdirSync(folderPath, { withFileTypes: true });
        const files = dirEntries.filter(entry => entry.isFile());
        const dirs = dirEntries.filter(entry => entry.isDirectory());

        if (files.length === 1 && dirs.length === 0) {
          // We have a single file in a temp directory - download it directly
          const singleFile = files[0];
          const filePath = path.join(folderPath, singleFile.name);
          console.log(`Downloading single file directly from temp directory: ${singleFile.name}`);
          return res.download(filePath, singleFile.name);
        }
      } catch (err) {
        console.error('Error checking directory contents:', err);
      }
    }

    // Set headers for zip file download
    res.setHeader('Content-Type', 'application/zip');
    res.setHeader('Content-Disposition', `attachment; filename="${folderName}.zip"`);

    // Create a zip archive stream with low compression level for speed
    const archive = archiver('zip', {
      zlib: { level: 2 } // Lower compression level (0-9) for faster processing
    });

    // Pipe the archive to the response
    archive.pipe(res);

    // Log any warnings during archiving
    archive.on('warning', (err) => {
      if (err.code === 'ENOENT') {
        console.warn('Archive warning:', err);
      } else {
        console.error('Archive error:', err);
      }
    });

    // Log any errors during archiving
    archive.on('error', (err) => {
      console.error('Archive error:', err);
      // The response has already started, so we can't send an error status
      // Just end the response
      res.end();
    });

    // Add the entire directory to the archive
    console.log(`Creating zip archive for folder: ${folderPath}`);
    archive.directory(folderPath, false);

    // Finalize the archive
    await archive.finalize();
    console.log('Zip archive created and sent successfully');
  } catch (error) {
    console.error('Folder download error:', error);
    // Only send error if headers haven't been sent yet
    if (!res.headersSent) {
      res.status(500).send(`Error downloading folder: ${error.message}`);
    } else {
      res.end();
    }
  }
});



// Function to wait until file size stabilizes
async function waitForFileSizeStabilization(filePath, maxWaitTimeMs = 60000, stabilityTimeMs = 5000) {
  const startTime = Date.now();
  let lastSize = -1;
  let lastSizeChangeTime = startTime;

  while (Date.now() - startTime < maxWaitTimeMs) {
    try {
      const stats = await fsPromises.stat(filePath);
      const currentSize = stats.size;

      // If file exists but size is 0, keep waiting
      if (currentSize === 0) {
        await new Promise(resolve => setTimeout(resolve, 1000));
        continue;
      }

      // If size changed, update the last change time
      if (currentSize !== lastSize) {
        console.log(`File size changed: ${lastSize} -> ${currentSize} bytes`);
        lastSize = currentSize;
        lastSizeChangeTime = Date.now();
      }
      // If size hasn't changed for stabilityTimeMs, consider it stable
      else if (Date.now() - lastSizeChangeTime >= stabilityTimeMs) {
        console.log(`File size stable at ${currentSize} bytes for ${stabilityTimeMs}ms`);
        return {
          isStable: true,
          finalSize: currentSize
        };
      }

      // Wait before checking again
      await new Promise(resolve => setTimeout(resolve, 1000));
    } catch (error) {
      // If file doesn't exist yet, keep waiting
      console.log(`Waiting for file to appear: ${filePath}`);
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
  }

  // If we reached the max wait time, return false
  return {
    isStable: false,
    finalSize: lastSize
  };
}

app.post('/api/compress-video', async (req, res) => {
  try {
    if (!req.files || !req.files.video) {
      return res.status(400).send('No video file was uploaded.');
    }

    const videoFile = req.files.video;

    // Check if the uploaded file is an MP4
    if (!videoFile.name.toLowerCase().endsWith('.mp4')) {
      return res.status(400).send('Only MP4 files are supported.');
    }

    // Generate a unique ID for this compression job
    const compressionId = Date.now().toString();
    const originalFilename = `original_${compressionId}.mp4`;
    const outputFilename = `compressed_${compressionId}.mp4`;

    // Define the destination path in the video bash directory
    const videoPath = path.join(videoBashDir, originalFilename);

    console.log(`Starting video upload to: ${videoPath}`);

    let originalFileSize = 0;

    try {
      // If the file is already in a temporary location, just copy it
      if (videoFile.tempFilePath && fs.existsSync(videoFile.tempFilePath)) {
        console.log(`Copying from temp file: ${videoFile.tempFilePath}`);
        await fsPromises.copyFile(videoFile.tempFilePath, videoPath);
      } else {
        // Otherwise, write the data directly to the target location
        console.log('Writing file from buffer...');
        await fsPromises.writeFile(videoPath, videoFile.data);
      }

      // Verify the file exists and has the correct size
      const stats = await fsPromises.stat(videoPath);
      originalFileSize = stats.size;
      console.log(`File saved. Size: ${originalFileSize} bytes`);

      if (originalFileSize === 0) {
        throw new Error('Saved file has zero size.');
      }
    } catch (fileErr) {
      console.error('Error saving video file:', fileErr);
      return res.status(500).send(`Error saving video file: ${fileErr.message}`);
    }

    // Run the compression script from the video directory
    console.log(`Running compression script on: ${originalFilename}`);

    // Start compression process
    const compressionProcess = execPromise(`cd "${videoBashDir}" && "${videoScriptPath}" "${originalFilename}"`);

    // Create list of potential output file paths to monitor
    // Adjust these patterns based on how your script names compressed files original_1745517834776_zmt.mp4
    const potentialOutputFiles = [
      path.join(videoBashDir, `encoded/original_${compressionId}_zmt.mp4`)
    ];

    console.log(`potentialOutputFiles:`,potentialOutputFiles);

    // Wait for compression script to finish
    try {
      const { stdout, stderr } = await compressionProcess;
      console.log('Compression stdout:', stdout);
      if (stderr) {
        console.error('Compression stderr:', stderr);
      }
      console.log('Compression script finished, monitoring output files for stabilization');
    } catch (execErr) {
      console.error('Compression script error:', execErr);

      // Clean up the original file
      try {
        await fsPromises.unlink(videoPath);
      } catch (unlinkErr) {
        console.error('Error removing original file after script failed:', unlinkErr);
      }

      return res.status(500).send(`Error running compression script: ${execErr.message}`);
    }
    console.log('Starting Potential output files');
    // Find all MP4 files in the directory
    const files = await fsPromises.readdir(videoBashDir);
    console.log('output files ', files);
    const mpegFiles = files.filter(file =>
      file !== originalFilename &&
      file.endsWith('.mp4') &&
      !file.includes('temp') &&
      !file.includes('_bak')
    );

    console.log('Potential output files:', mpegFiles);

    // Add any found MP4 files to our monitoring list that weren't already included
    mpegFiles.forEach(file => {
      const filePath = path.join(videoBashDir, file);
      if (!potentialOutputFiles.includes(filePath)) {
        potentialOutputFiles.push(filePath);
      }
    });

    // Look for the most recently created file that exists
    let encodedFilePath = null;
    let mostRecentTime = 0;

    for (const filePath of potentialOutputFiles) {
      try {
        const stats = await fsPromises.stat(filePath);
        if (stats.birthtimeMs > mostRecentTime) {
          mostRecentTime = stats.birthtimeMs;
          encodedFilePath = filePath;
        }
      } catch (err) {
        // File doesn't exist, skip it
      }
    }

    if (!encodedFilePath) {
      console.error('Could not find any potential output files');
      // Clean up the original file
      try {
        await fsPromises.unlink(videoPath);
      } catch (unlinkErr) {
        console.error('Error removing original file:', unlinkErr);
      }

      return res.status(500).send('Could not find the encoded video file after compression.');
    }

    console.log(`Found potential encoded file: ${encodedFilePath}`);

    // Wait for the file size to stabilize
    console.log(`Waiting for file size to stabilize: ${encodedFilePath}`);
    const { isStable, finalSize } = await waitForFileSizeStabilization(encodedFilePath);

    if (!isStable) {
      console.error('File size did not stabilize within the allowed time');
      // Still proceed but with a warning
      console.warn('Proceeding anyway with final size:', finalSize);
    }

    // Verify the encoded file has content
    try {
      const encodedStats = await fsPromises.stat(encodedFilePath);

      if (encodedStats.size === 0) {
        throw new Error('Compressed file has zero size. Compression may not be complete.');
      }

      console.log(`Encoded file size: ${encodedStats.size} bytes (${(encodedStats.size / originalFileSize * 100).toFixed(2)}% of original)`);

      // Move the encoded file to our encoded directory
      const encodedFileName = path.basename(encodedFilePath);
      const encodedDestPath = path.join(encodedDir, outputFilename);

      await fsPromises.copyFile(encodedFilePath, encodedDestPath);
      console.log(`Copied encoded file to: ${encodedDestPath}`);

      // Verify the copied file size
      const finalStats = await fsPromises.stat(encodedDestPath);
      if (finalStats.size === 0) {
        throw new Error('Final compressed file has zero size after copy.');
      }

      // Clean up original files
      await fsPromises.unlink(videoPath);
      await fsPromises.unlink(encodedFilePath);

      // Calculate compression ratio
      const compressionRatio = (finalStats.size / originalFileSize).toFixed(4);
      const savingsPercent = ((1 - finalStats.size / originalFileSize) * 100).toFixed(2);

      res.json({
        success: true,
        message: 'Video compressed successfully',
        compressionId: compressionId,
        originalName: videoFile.name,
        encodedName: outputFilename,
        originalSize: originalFileSize,
        compressedSize: finalStats.size,
        compressionRatio: compressionRatio,
        spaceReduction: `${savingsPercent}%`,
        stable: isStable
      });
    } catch (fileError) {
      console.error('Error verifying or moving encoded file:', fileError);
      return res.status(500).send(`Error processing compressed file: ${fileError.message}`);
    }
  } catch (error) {
    console.error('Video compression error:', error);
    res.status(500).send(`Error during video compression: ${error.message}`);
  }
});

// Endpoint to download a compressed video
app.get('/api/compressed-videos/:filename', (req, res) => {
  try {
    const { filename } = req.params;
    const filePath = path.join(encodedDir, filename);

    if (!fs.existsSync(filePath)) {
      return res.status(404).send('Compressed video file not found');
    }

    res.download(filePath);
  } catch (error) {
    console.error('Download error:', error);
    res.status(500).send(`Error downloading compressed video: ${error.message}`);
  }
});

// Endpoint to list all compressed videos
app.get('/api/compressed-videos', (req, res) => {
  try {
    const files = fs.readdirSync(encodedDir)
      .filter(file => file.toLowerCase().endsWith('.mp4'))
      .map(file => {
        const filePath = path.join(encodedDir, file);
        const stats = fs.statSync(filePath);
        return {
          name: file,
          size: stats.size,
          created: stats.birthtime
        };
      });

    res.json({
      success: true,
      videos: files
    });
  } catch (error) {
    console.error('List compressed videos error:', error);
    res.status(500).send(`Error listing compressed videos: ${error.message}`);
  }
});


// Start the server
const server = app.listen(port, () => {
  console.log(`ZMT Archiver API running on port ${port}`);
});
server.setTimeout(10 * 60 * 1000);
