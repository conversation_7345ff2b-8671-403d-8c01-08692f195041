const fs = require('fs');
const path = require('path');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);

// Test the DICOM compression integration
async function testDicomCompression() {
  try {
    console.log('Testing DICOM compression integration...');

    // Create a test DICOM file
    const testDicomContent = `This is a mock DICOM file for testing purposes.
In a real scenario, this would be a binary DICOM file with medical imaging data.
This file simulates a DICOM file with the .dcm extension.

DICOM Header Information:
- Patient ID: TEST001
- Study Date: 20241201
- Modality: CT
- Image Type: ORIGINAL\\PRIMARY\\AXIAL

Mock DICOM data follows...
[Binary DICOM data would be here in a real file]

This file is used to test the DICOM compression functionality
in the ZMT API system.`;

    const testInputFile = 'test_input.dcm';
    const testOutputFile = 'test_output_compressed.dcm';

    // Write test DICOM file
    fs.writeFileSync(testInputFile, testDicomContent);
    console.log(`Created test DICOM file: ${testInputFile}`);

    // Test the zmtdcm tool directly
    console.log('Testing zmtdcm tool...');
    const { stdout, stderr } = await exec(`./zmtdcm "${testInputFile}" "${testOutputFile}"`);

    console.log('DICOM compression stdout:', stdout);
    if (stderr) {
      console.log('DICOM compression stderr:', stderr);
    }

    // Check if output file was created
    if (fs.existsSync(testOutputFile)) {
      const inputStats = fs.statSync(testInputFile);
      const outputStats = fs.statSync(testOutputFile);

      console.log(`✅ DICOM compression successful!`);
      console.log(`   Input file: ${testInputFile} (${inputStats.size} bytes)`);
      console.log(`   Output file: ${testOutputFile} (${outputStats.size} bytes)`);
      console.log(`   Compression ratio: ${((1 - outputStats.size / inputStats.size) * 100).toFixed(2)}%`);

      // Clean up test files
      fs.unlinkSync(testInputFile);
      fs.unlinkSync(testOutputFile);
      console.log('✅ Test files cleaned up');

    } else {
      console.log('❌ Output file was not created');
    }

  } catch (error) {
    console.error('❌ Error testing DICOM compression:', error.message);
  }
}

// Test the isDicomFile function
function testIsDicomFile() {
  console.log('\nTesting DICOM file detection...');

  const testFiles = [
    'test.dcm',
    'test.dicom',
    'test.dic',
    'test.ima',
    'test.img',
    'test.DCM',
    'test.DICOM',
    'test.jpg',
    'test.png',
    'test.txt'
  ];

  // Simple isDicomFile function for testing
  function isDicomFile(fileName) {
    const lowerFileName = fileName.toLowerCase();
    const dicomExtensions = ['.dcm', '.dicom', '.dic', '.ima', '.img'];
    return dicomExtensions.some(ext => lowerFileName.endsWith(ext));
  }

  testFiles.forEach(file => {
    const isDicom = isDicomFile(file);
    console.log(`   ${file}: ${isDicom ? '✅ DICOM' : '❌ Not DICOM'}`);
  });
}

// Run tests
async function runTests() {
  console.log('=== DICOM Integration Tests ===\n');

  testIsDicomFile();
  await testDicomCompression();

  console.log('\n=== Tests Complete ===');
}

runTests();
