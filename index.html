<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>ZMT File Manager</title>
  <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
  <style>
    :root {
      --primary-color: #2c3e50;
      --secondary-color: #3498db;
      --accent-color: #e74c3c;
      --bg-color: #f9f9f9;
      --card-bg: #ffffff;
      --border-color: #e0e0e0;
      --success-color: #2ecc71;
      --warning-color: #f39c12;
    }

    * {
      margin: 0;
      padding: 0;
      box-sizing: border-box;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    }

    body {
      background-color: var(--bg-color);
      color: var(--primary-color);
      line-height: 1.6;
    }

    .container {
      max-width: 1200px;
      margin: 0 auto;
      padding: 20px;
    }

    header {
      background-color: var(--primary-color);
      color: white;
      padding: 20px 0;
      margin-bottom: 30px;
    }

    header h1 {
      text-align: center;
    }

    .card {
      background-color: var(--card-bg);
      border-radius: 8px;
      box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
      margin-bottom: 30px;
      overflow: hidden;
    }

    .card-header {
      background-color: var(--secondary-color);
      color: white;
      padding: 15px 20px;
      font-size: 18px;
      font-weight: 600;
    }

    .card-body {
      padding: 20px;
    }

    .form-group {
      margin-bottom: 20px;
    }

    label {
      display: block;
      margin-bottom: 8px;
      font-weight: 500;
    }

    input[type="text"],
    input[type="file"] {
      width: 100%;
      padding: 10px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      font-size: 16px;
    }

    .file-info {
      margin-top: 10px;
      font-size: 14px;
      color: #666;
    }

    button {
      background-color: var(--secondary-color);
      color: white;
      border: none;
      padding: 10px 20px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 16px;
      transition: background-color 0.3s;
    }

    button:hover {
      background-color: #2980b9;
    }

    button:disabled {
      background-color: #95a5a6;
      cursor: not-allowed;
    }

    .progress-container {
      width: 100%;
      background-color: #ddd;
      border-radius: 4px;
      margin-top: 15px;
      display: none;
    }

    .progress-bar {
      height: 20px;
      background-color: var(--success-color);
      border-radius: 4px;
      width: 0%;
      transition: width 0.3s;
      text-align: center;
      color: white;
      line-height: 20px;
      font-size: 14px;
    }

    .file-list {
      list-style: none;
      margin-top: 15px;
    }

    .file-item {
      display: flex;
      justify-content: space-between;
      align-items: center;
      padding: 12px 15px;
      border-bottom: 1px solid var(--border-color);
    }

    .file-item:last-child {
      border-bottom: none;
    }

    .file-details {
      flex: 1;
    }

    .file-name {
      font-weight: 500;
      margin-bottom: 5px;
    }

    .file-meta {
      font-size: 14px;
      color: #666;
    }

    .file-actions {
      display: flex;
      gap: 10px;
    }

    .btn-download, .btn-extract, .btn-delete {
      background-color: var(--secondary-color);
      color: white;
      border: none;
      padding: 8px 12px;
      border-radius: 4px;
      cursor: pointer;
      font-size: 14px;
      transition: background-color 0.3s;
    }

    .btn-extract {
      background-color: var(--warning-color);
    }

    .btn-delete {
      background-color: var(--accent-color);
    }

    .btn-download:hover {
      background-color: #2980b9;
    }

    .btn-extract:hover {
      background-color: #e67e22;
    }

    .btn-delete:hover {
      background-color: #c0392b;
    }



    .tab-container {
      margin-bottom: 30px;
    }

    .tabs {
      display: flex;
      list-style: none;
      margin-bottom: 0;
      overflow: hidden;
      border-bottom: 1px solid var(--border-color);
    }

    .tab-item {
      padding: 12px 24px;
      cursor: pointer;
      background-color: #eee;
      border-right: 1px solid var(--border-color);
      transition: background-color 0.3s;
    }

    .tab-item:last-child {
      border-right: none;
    }

    .tab-item.active {
      background-color: var(--card-bg);
      border-bottom: 3px solid var(--secondary-color);
      font-weight: 500;
    }

    .tab-content {
      display: none;
    }

    .tab-content.active {
      display: block;
    }

    .alert {
      padding: 15px;
      margin-bottom: 20px;
      border-radius: 4px;
      display: none;
    }

    .alert-success {
      background-color: #d4edda;
      color: #155724;
      border: 1px solid #c3e6cb;
    }

    .alert-danger {
      background-color: #f8d7da;
      color: #721c24;
      border: 1px solid #f5c6cb;
    }

    .alert-info {
      background-color: #d1ecf1;
      color: #0c5460;
      border: 1px solid #bee5eb;
    }

    .spinner {
      display: inline-block;
      width: 18px;
      height: 18px;
      border: 3px solid rgba(255,255,255,.3);
      border-radius: 50%;
      border-top-color: #fff;
      animation: spin 1s ease-in-out infinite;
      margin-right: 10px;
    }

    @keyframes spin {
      to { transform: rotate(360deg); }
    }

    .hidden {
      display: none;
    }

    .file-type-icon {
      margin-right: 10px;
      font-size: 1.2em;
    }

    .upload-options {
      display: flex;
      margin-bottom: 10px;
    }

    .upload-option-btn {
      padding: 8px 15px;
      background-color: #f8f9fa;
      border: 1px solid #ddd;
      cursor: pointer;
      transition: all 0.3s;
      flex: 1;
      text-align: center;
      color: #666; /* Gray text for non-selected buttons */
    }

    .upload-option-btn:first-child {
      border-radius: 4px 0 0 4px;
    }

    .upload-option-btn:last-child {
      border-radius: 0 4px 4px 0;
    }

    .upload-option-btn.active {
      background-color: var(--secondary-color);
      color: white;
      border-color: var(--secondary-color);
    }

    .extraction-container {
      margin-top: 20px;
      display: none;
    }

    .extracted-files {
      margin-top: 15px;
      border: 1px solid var(--border-color);
      border-radius: 4px;
      overflow: hidden;
    }

    .statistics {
      background-color: #f0f8ff;
      padding: 15px;
      border-radius: 4px;
      margin-top: 15px;
      display: none;
    }

    .stat-row {
      display: flex;
      justify-content: space-between;
      margin-bottom: 5px;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
      .tab-item {
        padding: 10px 15px;
        font-size: 14px;
      }

      .file-item {
        flex-direction: column;
        align-items: flex-start;
      }

      .file-actions {
        margin-top: 10px;
        width: 100%;
        justify-content: flex-end;
      }
    }
  </style>
</head>
<body>
  <header>
    <div class="container">
      <h1>ZMT File Manager</h1>
    </div>
  </header>

  <div class="container">
    <div class="tab-container">
      <ul class="tabs">
        <li class="tab-item active" data-tab="upload">Upload Files</li>
        <li class="tab-item" data-tab="archives">Archives</li>
      </ul>
    </div>

    <!-- Upload Tab -->
    <div id="upload-tab" class="tab-content active">
      <div class="card">
        <div class="card-header">Upload Files</div>
        <div class="card-body">
          <div class="alert alert-success" id="success-alert">
            Operation completed successfully!
          </div>
          <div class="alert alert-danger" id="error-alert">
            An error occurred. Please try again.
          </div>

          <div class="form-group">
            <label for="file-upload">Select Files</label>
            <!-- Folder upload option hidden for now -->
            <!--
            <div class="upload-options">
              <button type="button" id="select-files-btn" class="upload-option-btn active">Files</button>
              <button type="button" id="select-folder-btn" class="upload-option-btn">Folder</button>
            </div>
            -->
            <input type="file" id="file-upload" name="file" multiple>
            <input type="file" id="folder-upload" name="file" webkitdirectory directory multiple style="display: none;">
            <div class="file-info" id="file-info"></div>
          </div>

          <div class="form-group" id="archive-name-group">
            <label for="archive-name">Archive Name</label>
            <input type="text" id="archive-name" name="archiveName" placeholder="e.g., my_archive.zmt">
          </div>

          <button id="process-btn">
            <span class="spinner hidden" id="process-spinner"></span>
            <span id="process-btn-text">Process File</span>
          </button>

          <div class="progress-container" id="progress-container">
            <div class="progress-bar" id="progress-bar">0%</div>
          </div>

          <div class="statistics" id="compression-stats">
            <h3>Compression Results</h3>
            <div class="stat-row">
              <span>Original Size:</span>
              <span id="original-size">0</span>
            </div>
            <div class="stat-row">
              <span>Compressed Size:</span>
              <span id="compressed-size">0</span>
            </div>
            <div class="stat-row">
              <span>Space Reduction:</span>
              <span id="space-reduction">0%</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Archives Tab -->
    <div id="archives-tab" class="tab-content">
      <div class="card">
        <div class="card-header">Archives</div>
        <div class="card-body">
          <div class="alert alert-info" id="archive-loading">
            Loading archives...
          </div>
          <button id="refresh-archives" class="refresh-btn">
            <i class="fas fa-sync-alt"></i> Refresh List
          </button>
          <ul class="file-list" id="archive-list"></ul>

          <div class="extraction-container" id="extraction-container">
            <h3>Extracted Files</h3>
            <p id="extraction-info"></p>
            <div class="extracted-files" id="extracted-files-list"></div>
          </div>
        </div>
      </div>
    </div>

    <!-- Videos Tab (Hidden) -->
    <div id="videos-tab" class="tab-content" style="display: none;">
      <div class="card">
        <div class="card-header">Compressed Videos</div>
        <div class="card-body">
          <div class="alert alert-info" id="video-loading">
            Loading videos...
          </div>
          <button id="refresh-videos" class="refresh-btn">
            <i class="fas fa-sync-alt"></i> Refresh List
          </button>
          <ul class="file-list" id="video-list"></ul>
        </div>
      </div>
    </div>
  </div>

  <script>
    // Utility functions
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    function formatDate(dateString) {
      const date = new Date(dateString);
      return date.toLocaleString();
    }

    function getFileIcon(fileName) {
      const extension = fileName.split('.').pop().toLowerCase();
      switch(extension) {
        case 'pdf':
          return '<i class="fas fa-file-pdf file-type-icon"></i>';
        case 'txt':
        case 'log':
        case 'md':
          return '<i class="fas fa-file-alt file-type-icon"></i>';
        case 'db':
        case 'sqlite':
        case 'db3':
          return '<i class="fas fa-database file-type-icon"></i>';
        case 'mp4':
        case 'mov':
        case 'avi':
          return '<i class="fas fa-file-video file-type-icon"></i>';
        case 'zmt':
          return '<i class="fas fa-file-archive file-type-icon"></i>';
        case 'dcm':
        case 'dicom':
        case 'dic':
        case 'ima':
        case 'img':
          return '<i class="fas fa-file-medical file-type-icon" style="color: #e74c3c;"></i>';
        default:
          return '<i class="fas fa-file file-type-icon"></i>';
      }
    }

    function showAlert(type, message) {
      const successAlert = document.getElementById('success-alert');
      const errorAlert = document.getElementById('error-alert');

      successAlert.style.display = 'none';
      errorAlert.style.display = 'none';

      if (type === 'success') {
        successAlert.textContent = message;
        successAlert.style.display = 'block';
      } else {
        errorAlert.textContent = message;
        errorAlert.style.display = 'block';
      }

      // Auto hide after 5 seconds
      setTimeout(() => {
        if (type === 'success') {
          successAlert.style.display = 'none';
        } else {
          errorAlert.style.display = 'none';
        }
      }, 5000);
    }

    // Tab functionality
    document.querySelectorAll('.tab-item').forEach(tab => {
      tab.addEventListener('click', () => {
        // Remove active class from all tabs
        document.querySelectorAll('.tab-item').forEach(t => {
          t.classList.remove('active');
        });

        // Hide all tab contents
        document.querySelectorAll('.tab-content').forEach(content => {
          content.classList.remove('active');
        });

        // Add active class to clicked tab
        tab.classList.add('active');

        // Show the corresponding tab content
        const tabId = tab.getAttribute('data-tab');
        document.getElementById(tabId + '-tab').classList.add('active');

        // Load content when switching to archives tab
        if (tabId === 'archives') {
          loadArchives();
        }
      });
    });

    // File Upload Handler
    const fileUpload = document.getElementById('file-upload');
    const folderUpload = document.getElementById('folder-upload');
    const selectFilesBtn = document.getElementById('select-files-btn');
    const selectFolderBtn = document.getElementById('select-folder-btn');
    const fileInfo = document.getElementById('file-info');
    const processBtn = document.getElementById('process-btn');
    const processSpinner = document.getElementById('process-spinner');
    const processBtnText = document.getElementById('process-btn-text');
    const progressContainer = document.getElementById('progress-container');
    const progressBar = document.getElementById('progress-bar');
    const archiveNameGroup = document.getElementById('archive-name-group');
    const compressionStats = document.getElementById('compression-stats');

    // Toggle between file and folder upload - only if the buttons exist
    if (selectFilesBtn && selectFolderBtn) {
      selectFilesBtn.addEventListener('click', () => {
        selectFilesBtn.classList.add('active');
        selectFolderBtn.classList.remove('active');
        fileUpload.style.display = 'block';
        folderUpload.style.display = 'none';
        fileInfo.textContent = '';
        processBtn.disabled = true;
      });

      selectFolderBtn.addEventListener('click', () => {
        selectFolderBtn.classList.add('active');
        selectFilesBtn.classList.remove('active');
        folderUpload.style.display = 'block';
        fileUpload.style.display = 'none';
        fileInfo.textContent = '';
        processBtn.disabled = true;
      });
    }

    // Handle file selection
    fileUpload.addEventListener('change', (e) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        let totalSize = 0;
        let fileCount = files.length;
        let hasRestrictedFiles = false;
        let restrictedFileNames = [];

        // Check for restricted file types (images and videos)
        const restrictedExtensions = [
          // Images
          '.jpg', '.jpeg', '.png', '.gif', '.bmp', '.webp', '.tiff', '.svg',
          // Videos
          '.mp4', '.mov', '.avi', '.mkv', '.wmv', '.flv', '.webm', '.m4v', '.mpg', '.mpeg'
        ];

        // Allowed special file types (these override the restricted extensions)
        const allowedSpecialTypes = [
          // DICOM files
          '.dcm', '.dicom', '.dic', '.ima', '.img'
        ];

        // Calculate total size and check for restricted files
        for (let i = 0; i < files.length; i++) {
          totalSize += files[i].size;

          // Check if file has a restricted extension
          const fileName = files[i].name.toLowerCase();

          // First check if it's an allowed special type (like DICOM)
          const isAllowedSpecialType = allowedSpecialTypes.some(ext => fileName.endsWith(ext));

          // Then check if it has a restricted extension
          const hasRestrictedExt = restrictedExtensions.some(ext => fileName.endsWith(ext));

          // Only mark as restricted if it has a restricted extension and is not an allowed special type
          if (hasRestrictedExt && !isAllowedSpecialType) {
            hasRestrictedFiles = true;
            restrictedFileNames.push(files[i].name);
          }
        }

        // If there are restricted files, show error and disable process button
        if (hasRestrictedFiles) {
          fileInfo.innerHTML = `
            <div class="alert alert-danger">
              <strong>Error:</strong> Image and video files are not allowed.
              <p>Please remove the following files:</p>
              <ul style="margin-bottom: 0;">
                ${restrictedFileNames.map(name => `<li>${name}</li>`).join('')}
              </ul>
              <p style="margin-top: 10px; margin-bottom: 0;">
                <small>Note: DICOM medical image files (.dcm, .dicom, etc.) are allowed.</small>
              </p>
            </div>`;
          processBtn.disabled = true;
          return;
        }

        // Update UI based on selection
        fileInfo.textContent = `Selected: ${fileCount} ${fileCount === 1 ? 'file' : 'files'} (${formatFileSize(totalSize)})`;

        // Force enable the process button
        processBtn.disabled = false;
        processBtn.removeAttribute('disabled');

        // Always show archive name and set button text to "Archive Files"
        archiveNameGroup.style.display = 'block';
        processBtnText.textContent = 'Archive Files';
      } else {
        fileInfo.textContent = '';
        processBtn.disabled = true;
      }
    });

    // Handle folder selection
    folderUpload.addEventListener('change', (e) => {
      const files = e.target.files;
      if (files && files.length > 0) {
        let totalSize = 0;
        let fileCount = files.length;
        let folderNames = new Set();

        // Calculate total size and identify folders
        for (let i = 0; i < files.length; i++) {
          totalSize += files[i].size;

          // Extract folder name from path
          if (files[i].webkitRelativePath) {
            const folderPath = files[i].webkitRelativePath.split('/');
            if (folderPath.length > 1) {
              folderNames.add(folderPath[0]);
            }
          }
        }

        const folderCount = folderNames.size;

        // Update UI based on selection
        fileInfo.textContent = `Selected: ${folderCount} ${folderCount === 1 ? 'folder' : 'folders'} containing ${fileCount} files (${formatFileSize(totalSize)})`;
        processBtn.disabled = false;
        processBtnText.textContent = 'Archive Folder';
      } else {
        fileInfo.textContent = '';
        processBtn.disabled = true;
      }
    });

    // Process Button Handler
    processBtn.addEventListener('click', async (e) => {
      console.log('Process button clicked');
      e.preventDefault(); // Prevent any default form submission

      // Since folder upload is hidden, we're always in file mode
      const files = fileUpload.files;
      console.log('Files selected:', files ? files.length : 0);

      if (!files || files.length === 0) {
        console.log('No files selected, returning');
        return;
      }

      // Reset UI
      progressContainer.style.display = 'block';
      progressBar.style.width = '0%';
      progressBar.textContent = '0%';
      processBtn.disabled = true;
      processSpinner.classList.remove('hidden');
      compressionStats.style.display = 'none';

      const formData = new FormData();
      console.log('FormData created');

      // Handle archiving files
      // Add all files to formData
      for (let i = 0; i < files.length; i++) {
        // Use the file name
        const filePath = files[i].name;
        formData.append('file', files[i], filePath);
        console.log(`Added file to formData: ${filePath}`);
      }

      // Set archive name
      let archiveName = document.getElementById('archive-name').value;
      console.log('Archive name from input:', archiveName);

      // Ensure the archive name has the .zmt extension
      if (archiveName) {
        if (!archiveName.toLowerCase().endsWith('.zmt')) {
          archiveName += '.zmt';
        }
        formData.append('archiveName', archiveName);
        console.log('Using provided archive name:', archiveName);
      } else {
        // Generate a default name based on timestamp
        const defaultName = `archive_${Date.now()}`;
        formData.append('archiveName', `${defaultName}.zmt`);
        console.log('Using generated archive name:', `${defaultName}.zmt`);
      }

      // Use direct upload instead of chunked upload
      try {
        console.log('Starting direct upload');
        processBtnText.textContent = 'Uploading...';

        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/archive', true);

        xhr.upload.onprogress = (e) => {
          if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            progressBar.style.width = percentComplete + '%';
            progressBar.textContent = percentComplete + '%';
            console.log(`Upload progress: ${percentComplete}%`);
          }
        };

        xhr.onload = function() {
          console.log('Upload completed with status:', xhr.status);
          if (xhr.status === 200) {
            try {
              const response = JSON.parse(xhr.responseText);
              console.log('Upload success response:', response);

              // Create success message based on response
              let successMessage = '';
              if (response.dicomFiles && response.dicomFiles.length > 0) {
                // Handle DICOM files
                if (response.archiveName) {
                  // Mixed upload: DICOM + other files
                  successMessage = `Processed ${response.dicomFiles.length} DICOM files and archived other files as ${response.archiveName}`;
                } else {
                  // DICOM-only upload
                  successMessage = `Processed ${response.dicomFiles.length} DICOM file${response.dicomFiles.length > 1 ? 's' : ''} successfully`;
                }
              } else if (response.archiveName) {
                // Regular archive
                if (files.length > 1) {
                  successMessage = `${files.length} files archived successfully as ${response.archiveName}`;
                } else {
                  successMessage = `File archived successfully as ${response.archiveName}`;
                }
              } else {
                // Fallback
                successMessage = response.message || 'Files processed successfully';
              }

              showAlert('success', successMessage);
              resetUploadForm();
            } catch (error) {
              console.error('Error parsing response:', error);
              showAlert('error', 'Error processing server response');
            }
          } else {
            console.error('Upload failed with status:', xhr.status);
            console.error('Response:', xhr.responseText);
            showAlert('error', `Error: ${xhr.responseText || 'Upload failed'}`);
          }

          processBtn.disabled = false;
          processSpinner.classList.add('hidden');
          processBtnText.textContent = 'Archive Files';
        };

        xhr.onerror = function(e) {
          console.error('Network error during upload:', e);
          showAlert('error', 'Network error occurred during upload');
          processBtn.disabled = false;
          processSpinner.classList.add('hidden');
          processBtnText.textContent = 'Archive Files';
        };

        console.log('Sending form data');
        xhr.send(formData);
      } catch (error) {
        console.error('Error in upload process:', error);
        showAlert('error', `Error: ${error.message}`);
        processBtn.disabled = false;
        processSpinner.classList.add('hidden');
        processBtnText.textContent = 'Archive Files';
      }
    });

    // Archive files function with chunked upload support
    async function archiveFile(formData) {
      try {
        console.log('archiveFile function called');
        processBtnText.textContent = 'Uploading...';
        progressBar.style.width = '0%';
        progressBar.textContent = '0%';

        // Get archive name from formData
        const archiveName = formData.get('archiveName');
        console.log('Archive name from formData:', archiveName);

        // Get all files to upload
        const files = formData.getAll('files');
        console.log('Files from formData:', files.length);

        // Log file details
        files.forEach((file, index) => {
          console.log(`File ${index + 1}:`, file.name, file.size, 'bytes');
        });

        const hasFolder = Array.from(fileUpload.files).some(file => file.webkitRelativePath && file.webkitRelativePath.includes('/'));
        console.log('Has folder:', hasFolder);

        // Initialize session for chunked upload
        const sessionId = Date.now().toString();
        console.log('Session ID:', sessionId);
        let uploadedFiles = 0;
        const totalFiles = files.length;

        // Create a function to upload a single file in chunks
        async function uploadFileInChunks(file, relativePath) {
          console.log(`Starting to upload file: ${file.name}, path: ${relativePath}`);
          const CHUNK_SIZE = 1024 * 1024; // 1MB chunks
          const totalChunks = Math.ceil(file.size / CHUNK_SIZE);
          console.log(`File size: ${file.size} bytes, total chunks: ${totalChunks}`);
          let currentChunk = 0;

          while (currentChunk < totalChunks) {
            // Calculate chunk boundaries
            const start = currentChunk * CHUNK_SIZE;
            const end = Math.min(file.size, start + CHUNK_SIZE);
            const chunk = file.slice(start, end);
            console.log(`Processing chunk ${currentChunk + 1}/${totalChunks}, size: ${chunk.size} bytes`);

            // Create formData for this chunk
            const chunkFormData = new FormData();
            chunkFormData.append('sessionId', sessionId);
            chunkFormData.append('chunkIndex', currentChunk);
            chunkFormData.append('totalChunks', totalChunks);
            chunkFormData.append('fileName', file.name);
            chunkFormData.append('relativePath', relativePath);
            chunkFormData.append('fileSize', file.size);
            chunkFormData.append('totalFiles', totalFiles);
            chunkFormData.append('archiveName', archiveName);
            chunkFormData.append('chunk', chunk);
            console.log('ChunkFormData created with all parameters');

            // Upload the chunk
            console.log(`Uploading chunk ${currentChunk + 1}/${totalChunks} to /api/upload-chunk`);
            try {
              await new Promise((resolve, reject) => {
                const xhr = new XMLHttpRequest();
                xhr.open('POST', '/api/upload-chunk', true);

                xhr.onload = function() {
                  if (xhr.status === 200) {
                    console.log(`Chunk ${currentChunk + 1}/${totalChunks} uploaded successfully`);
                    resolve();
                  } else {
                    console.error(`Error uploading chunk: ${xhr.status} ${xhr.statusText}`);
                    console.error(`Response: ${xhr.responseText}`);
                    reject(new Error(`Error uploading chunk: ${xhr.statusText}`));
                  }
                };

                xhr.onerror = function(e) {
                  console.error('Network error during chunk upload:', e);
                  reject(new Error('Network error occurred during chunk upload'));
                };

                xhr.send(chunkFormData);
              });
            } catch (error) {
              console.error(`Error uploading chunk ${currentChunk + 1}/${totalChunks}:`, error);
              throw error;
            }

            // Update progress
            currentChunk++;
            const fileProgress = (currentChunk / totalChunks) * 100;
            const overallProgress = Math.round(((uploadedFiles + (fileProgress / 100)) / totalFiles) * 100);
            progressBar.style.width = overallProgress + '%';
            progressBar.textContent = overallProgress + '%';
          }
        }

        // Upload each file in chunks
        console.log(`Starting to upload ${files.length} files`);
        for (let i = 0; i < files.length; i++) {
          const file = files[i];
          // Get the relative path for directory structure
          let relativePath = file.name;
          if (file.webkitRelativePath) {
            relativePath = file.webkitRelativePath;
          }
          console.log(`Processing file ${i + 1}/${files.length}: ${relativePath}`);

          try {
            await uploadFileInChunks(file, relativePath);
            uploadedFiles++;
            console.log(`File ${i + 1}/${files.length} uploaded successfully`);

            // Update progress after each file
            const overallProgress = Math.round((uploadedFiles / totalFiles) * 100);
            progressBar.style.width = overallProgress + '%';
            progressBar.textContent = overallProgress + '%';
          } catch (error) {
            console.error(`Error uploading file ${i + 1}/${files.length}:`, error);
            throw error;
          }
        }

        console.log('All files uploaded, finalizing upload');
        // All files uploaded, finalize the upload and create archive
        try {
          const finalizeData = {
            sessionId: sessionId,
            archiveName: archiveName,
            hasFolder: hasFolder
          };
          console.log('Finalize data:', finalizeData);

          const finalizeResponse = await fetch('/api/finalize-upload', {
            method: 'POST',
            headers: {
              'Content-Type': 'application/json'
            },
            body: JSON.stringify(finalizeData)
          });

          console.log('Finalize response status:', finalizeResponse.status);

          if (finalizeResponse.ok) {
            const response = await finalizeResponse.json();
            console.log('Finalize success response:', response);

            // Create success message based on what was uploaded
            let successMessage = '';
            if (hasFolder) {
              successMessage = `Folder archived successfully as ${response.archiveName}`;
            } else if (totalFiles > 1) {
              successMessage = `${totalFiles} files archived successfully as ${response.archiveName}`;
            } else {
              successMessage = `File archived successfully as ${response.archiveName}`;
            }

            showAlert('success', successMessage);
            resetUploadForm();
          } else {
            const errorText = await finalizeResponse.text();
            console.error('Finalize error response:', errorText);

            let errorData;
            try {
              errorData = JSON.parse(errorText);
            } catch (e) {
              errorData = { message: errorText };
            }

            showAlert('error', `Error: ${errorData.message || 'Failed to finalize upload'}`);
            processBtn.disabled = false;
          }
        } catch (error) {
          console.error('Error finalizing upload:', error);
          showAlert('error', `Error finalizing upload: ${error.message}`);
          processBtn.disabled = false;
        }

        processSpinner.classList.add('hidden');
        processBtnText.textContent = 'Archive Files';
      } catch (error) {
        showAlert('error', `Error: ${error.message}`);
        processBtn.disabled = false;
        processSpinner.classList.add('hidden');
        processBtnText.textContent = 'Archive Files';
      }
    }

    // Process video function
    async function processVideo(formData) {
      try {
        processBtnText.textContent = 'Compressing...';

        const xhr = new XMLHttpRequest();
        xhr.open('POST', '/api/compress-video', true);

        xhr.upload.onprogress = (e) => {
          if (e.lengthComputable) {
            const percentComplete = Math.round((e.loaded / e.total) * 100);
            progressBar.style.width = percentComplete + '%';
            progressBar.textContent = percentComplete + '%';
          }
        };

        xhr.onload = function() {
          if (xhr.status === 200) {
            const response = JSON.parse(xhr.responseText);
            progressBar.style.width = '100%';
            progressBar.textContent = 'Processing Complete';

            // Display compression statistics
            document.getElementById('original-size').textContent = formatFileSize(response.originalSize);
            document.getElementById('compressed-size').textContent = formatFileSize(response.compressedSize);
            document.getElementById('space-reduction').textContent = response.spaceReduction;
            compressionStats.style.display = 'block';

            showAlert('success', 'Video compressed successfully');
            resetUploadForm(false); // Don't hide the stats
          } else {
            showAlert('error', `Error: ${xhr.responseText}`);
            processBtn.disabled = false;
          }
          processSpinner.classList.add('hidden');
          processBtnText.textContent = 'Compress Video';
        };

        xhr.onerror = function() {
          showAlert('error', 'Network error occurred');
          processBtn.disabled = false;
          processSpinner.classList.add('hidden');
          processBtnText.textContent = 'Compress Video';
        };

        xhr.send(formData);
      } catch (error) {
        showAlert('error', `Error: ${error.message}`);
        processBtn.disabled = false;
        processSpinner.classList.add('hidden');
        processBtnText.textContent = 'Compress Video';
      }
    }

    // Reset the upload form
    function resetUploadForm(resetAll = true) {
      fileUpload.value = '';
      folderUpload.value = '';
      fileInfo.textContent = '';
      document.getElementById('archive-name').value = '';
      processBtn.disabled = true;

      if (resetAll) {
        progressContainer.style.display = 'none';
        compressionStats.style.display = 'none';
      }
    }

    // Load Archives
    async function loadArchives() {
      const archiveList = document.getElementById('archive-list');
      const archiveLoading = document.getElementById('archive-loading');

      archiveLoading.style.display = 'block';
      archiveList.innerHTML = '';

      try {
        const response = await fetch('/api/archives');
        const data = await response.json();

        archiveLoading.style.display = 'none';

        if (data.success && data.archives.length > 0) {
          data.archives.forEach(archive => {
            const li = document.createElement('li');
            li.className = 'file-item';

            // Check if it's a DICOM file or regular archive
            const isDicomFile = archive.type === 'dicom' || archive.name.toLowerCase().endsWith('.dcm');

            // Create different action buttons based on file type
            let actionButtons = '';
            if (isDicomFile) {
              // DICOM files get direct download
              actionButtons = `
                <button class="btn-download" data-name="${archive.name}" data-type="dicom">Download</button>
                <button class="btn-delete" data-name="${archive.name}">Delete</button>
              `;
            } else {
              // ZMT archives get extract option
              actionButtons = `
                <button class="btn-extract" data-name="${archive.name}">Extract</button>
                <button class="btn-delete" data-name="${archive.name}">Delete</button>
              `;
            }

            li.innerHTML = `
              <div class="file-details">
                ${getFileIcon(archive.name)}
                <div class="file-name">${archive.name}</div>
                <div class="file-meta">
                  Size: ${formatFileSize(archive.size)} | Created: ${formatDate(archive.created)}
                  ${isDicomFile ? ' | <span style="color: #e74c3c; font-weight: bold;">DICOM</span>' : ' | <span style="color: #3498db;">Archive</span>'}
                </div>
              </div>
              <div class="file-actions">
                ${actionButtons}
              </div>
            `;
            archiveList.appendChild(li);
          });

          // Add event listeners to extract buttons
          document.querySelectorAll('.btn-extract').forEach(btn => {
            btn.addEventListener('click', (e) => {
              extractArchive(e.target.getAttribute('data-name'));
            });
          });

          // Add event listeners to DICOM download buttons
          document.querySelectorAll('.btn-download[data-type="dicom"]').forEach(btn => {
            btn.addEventListener('click', (e) => {
              downloadDicomFile(e.target.getAttribute('data-name'));
            });
          });

          // Add event listeners to delete buttons
          document.querySelectorAll('.btn-delete').forEach(btn => {
            btn.addEventListener('click', (e) => {
              deleteArchive(e.target.getAttribute('data-name'));
            });
          });
        } else {
          archiveList.innerHTML = '<li class="file-item">No archives found</li>';
        }
      } catch (error) {
        archiveLoading.style.display = 'none';
        archiveList.innerHTML = `<li class="file-item">Error loading archives: ${error.message}</li>`;
      }
    }

    // Download DICOM File
    function downloadDicomFile(fileName) {
      console.log(`Downloading DICOM file: ${fileName}`);
      window.location.href = `/api/download-dicom/${fileName}`;
    }

    // Extract Archive
    async function extractArchive(archiveName) {
      const extractionContainer = document.getElementById('extraction-container');
      const extractionInfo = document.getElementById('extraction-info');
      const extractedFilesList = document.getElementById('extracted-files-list');

      // Check if it's a DICOM file - these can't be extracted
      if (archiveName.toLowerCase().endsWith('.dcm')) {
        showAlert('error', 'DICOM files cannot be extracted. Use the Download button instead.');
        return;
      }

      extractionContainer.style.display = 'none';
      extractedFilesList.innerHTML = '';

      try {
        const response = await fetch('/api/unarchive', {
          method: 'POST',
          headers: {
            'Content-Type': 'application/json'
          },
          body: JSON.stringify({ archiveName })
        });

        const data = await response.json();

        if (data.success) {
          // Display extraction message with timing information
          extractionInfo.textContent = data.message;
          extractionContainer.style.display = 'block';

          data.extractedFiles.forEach(file => {
            const fileItem = document.createElement('div');
            fileItem.className = 'file-item';
            fileItem.innerHTML = `
              <div class="file-details">
                ${getFileIcon(file)}
                <div class="file-name">${file}</div>
              </div>
              <div class="file-actions">
                <button class="btn-download" data-extraction="${data.extractionId}" data-file="${file}">Download</button>
              </div>
            `;
            extractedFilesList.appendChild(fileItem);
          });

          // Add event listeners to download buttons
          document.querySelectorAll(`button[data-extraction="${data.extractionId}"]`).forEach(btn => {
            btn.addEventListener('click', async (e) => {
              const extractionId = e.target.getAttribute('data-extraction');
              const fileName = e.target.getAttribute('data-file');

              // If the server told us this is a single file extraction, use direct download
              if (data.isSingleFile && data.singleFileName === fileName) {
                console.log('Using direct download for single file extraction');
                window.location.href = `/api/download/${extractionId}/${fileName}`;
                return;
              }

              try {
                // First check if it's a directory by making a request to the server
                const response = await fetch(`/api/check-path-type/${extractionId}/${fileName}`);
                if (!response.ok) {
                  throw new Error(`Failed to check path type: ${response.statusText}`);
                }

                const pathData = await response.json();

                if (pathData.isDirectory) {
                  if (pathData.isSingleFileInTempDir) {
                    // For temp directories with a single file, we'll still use the folder download endpoint
                    // but our server will handle it specially and download the file directly
                    console.log(`Detected single file in temp directory: ${pathData.singleFileName}`);
                    window.location.href = `/api/download-folder/${extractionId}/${fileName}`;
                  } else {
                    // For regular directories, use zip download
                    window.location.href = `/api/download-folder/${extractionId}/${fileName}`;
                  }
                } else {
                  // For regular files, use direct download
                  window.location.href = `/api/download/${extractionId}/${fileName}`;
                }
              } catch (error) {
                console.error('Error checking path type:', error);

                // Fallback to the old method if the server endpoint fails
                const hasExtension = fileName.lastIndexOf('.') > fileName.lastIndexOf('/');

                if (hasExtension) {
                  // For regular files, use direct download
                  window.location.href = `/api/download/${extractionId}/${fileName}`;
                } else {
                  // For directories, use zip download
                  window.location.href = `/api/download-folder/${extractionId}/${fileName}`;
                }
              }
            });
          });

        } else {
          // Check if it's a DICOM file error response
          if (data.isDicomFile && data.downloadUrl) {
            showAlert('error', data.message);
            // Optionally redirect to download
            // window.location.href = data.downloadUrl;
          } else {
            showAlert('error', data.message || 'Failed to extract archive');
          }
        }
      } catch (error) {
        showAlert('error', `Error extracting archive: ${error.message}`);
      }
    }

    // Load Compressed Videos
    async function loadCompressedVideos() {
      const videoList = document.getElementById('video-list');
      const videoLoading = document.getElementById('video-loading');

      videoLoading.style.display = 'block';
      videoList.innerHTML = '';

      try {
        const response = await fetch('/api/compressed-videos');
        const data = await response.json();

        videoLoading.style.display = 'none';

        if (data.success && data.videos.length > 0) {
          data.videos.forEach(video => {
            const li = document.createElement('li');
            li.className = 'file-item';
            li.innerHTML = `
              <div class="file-details">
                ${getFileIcon(video.name)}
                <div class="file-name">${video.name}</div>
                <div class="file-meta">
                  Size: ${formatFileSize(video.size)} | Created: ${formatDate(video.created)}
                </div>
              </div>
              <div class="file-actions">
                <button class="btn-download" data-name="${video.name}">Download</button>
              </div>
            `;
            videoList.appendChild(li);
          });

          // Add event listeners to download buttons
          document.querySelectorAll('#video-list .btn-download').forEach(btn => {
            btn.addEventListener('click', (e) => {
              downloadCompressedVideo(e.target.getAttribute('data-name'));
            });
          });
        } else {
          videoList.innerHTML = '<li class="file-item">No compressed videos found</li>';
        }
      } catch (error) {
        videoLoading.style.display = 'none';
        videoList.innerHTML = `<li class="file-item">Error loading videos: ${error.message}</li>`;
      }
    }

    // Delete Archive
    async function deleteArchive(archiveName) {
      // Confirm deletion with warning about extracted files
      if (!confirm(`Are you sure you want to delete the archive "${archiveName}"?\n\nWARNING: This will also delete any extracted files associated with this archive.\n\nThis action cannot be undone.`)) {
        return; // User cancelled
      }

      try {
        const response = await fetch(`/api/archives/${archiveName}`, {
          method: 'DELETE'
        });

        const data = await response.json();

        if (data.success) {
          let successMessage = `Archive "${archiveName}" deleted successfully`;

          // Add information about deleted extraction directories if available
          if (data.extractedDirectoriesDeleted !== undefined) {
            successMessage += `\n${data.extractedDirectoriesDeleted} extraction ${data.extractedDirectoriesDeleted === 1 ? 'directory' : 'directories'} cleaned up`;
          }

          showAlert('success', successMessage);

          // Hide extraction container if it's currently showing extracted files
          const extractionContainer = document.getElementById('extraction-container');
          extractionContainer.style.display = 'none';

          // Refresh the archive list
          loadArchives();
        } else {
          showAlert('error', 'Failed to delete archive');
        }
      } catch (error) {
        showAlert('error', `Error deleting archive: ${error.message}`);
      }
    }

    // Function to format file size in human-readable format
    function formatFileSize(bytes) {
      if (bytes === 0) return '0 Bytes';
      const k = 1024;
      const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB'];
      const i = Math.floor(Math.log(bytes) / Math.log(k));
      return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Download Compressed Video
    function downloadCompressedVideo(fileName) {
      window.location.href = `/api/compressed-videos/${fileName}`;
    }

    // Refresh list button
    document.getElementById('refresh-archives').addEventListener('click', loadArchives);

    // Initialize on page load
    document.addEventListener('DOMContentLoaded', () => {
      // Reset form
      resetUploadForm();

      // Check if files are already selected and enable the process button if needed
      const files = document.getElementById('file-upload').files;
      if (files && files.length > 0) {
        document.getElementById('process-btn').disabled = false;
        document.getElementById('process-btn').removeAttribute('disabled');
      }

      // Add a click handler to manually enable the button when clicked
      document.getElementById('process-btn').addEventListener('click', function(e) {
        // If the button is disabled but files are selected, enable it
        const files = document.getElementById('file-upload').files;
        if (this.disabled && files && files.length > 0) {
          this.disabled = false;
          this.removeAttribute('disabled');
          // Don't prevent default so the normal click handler runs
        }
      });

      // Load archives
      loadArchives();
    });
  </script>
</body>
</html>
