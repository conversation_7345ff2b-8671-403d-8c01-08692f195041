const fs = require('fs');
const path = require('path');
const archiver = require('archiver');
const { promisify } = require('util');
const exec = promisify(require('child_process').exec);

// Promisify for better async handling
const fsPromises = fs.promises;

// Path to the DICOM compression tool
const zmtdcmPath = './zmtdcm';

// Helper function to check if a file is a DICOM file
function isDicomFile(fileName) {
  const lowerFileName = fileName.toLowerCase();
  const dicomExtensions = ['.dcm', '.dicom', '.dic', '.ima', '.img'];
  return dicomExtensions.some(ext => lowerFileName.endsWith(ext));
}

// Helper function to compress DICOM files using zmtdcm
async function compressDicomFile(inputPath, outputDir) {
  try {
    const fileName = path.basename(inputPath);
    const nameWithoutExt = path.parse(fileName).name;
    const outputFileName = `${nameWithoutExt}_compressed.dcm`;
    const outputPath = path.join(outputDir, outputFileName);

    console.log(`Compressing DICOM file: ${inputPath} -> ${outputPath}`);

    // Ensure output directory exists
    await fsPromises.mkdir(outputDir, { recursive: true });

    // Run zmtdcm compression with correct syntax: zmtdcm input.dcm output.dcm
    const { stdout, stderr } = await exec(`${zmtdcmPath} "${inputPath}" "${outputPath}"`, {
      timeout: 5 * 60 * 1000 // 5 minutes timeout
    });

    console.log('DICOM compression stdout:', stdout);
    if (stderr) {
      console.error('DICOM compression stderr:', stderr);
    }

    // Check if the compressed file was created
    if (!fs.existsSync(outputPath)) {
      throw new Error(`Compressed DICOM file not found at expected location: ${outputPath}`);
    }

    // Verify the compressed file has content
    const compressedStats = await fsPromises.stat(outputPath);
    if (compressedStats.size === 0) {
      throw new Error('Compressed DICOM file has zero size');
    }

    console.log(`DICOM compression successful: ${fileName} (${compressedStats.size} bytes)`);
    return outputPath;
  } catch (error) {
    console.error('Error compressing DICOM file:', error);
    throw new Error(`Failed to compress DICOM file: ${error.message}`);
  }
}

/**
 * Compresses a file or directory using the appropriate compression method
 * - DICOM files: Uses zmtdcm tool and outputs .dcm files
 * - Other files/directories: Uses archiver package and outputs .zmt files
 * @param {string} localInputPath - Path to the file or directory to compress
 * @param {string} originalKey - Original S3 key to maintain folder hierarchy
 * @returns {Promise<{compressedFilePath: string, originalKey: string}>} - Path to the compressed file and original key
 */
async function compressFile(localInputPath, originalKey) {
  try {
    // Extract the filename and directory structure from the original key
    const filename = path.basename(originalKey);
    const dirPath = path.dirname(originalKey);

    // Create archives directory with the same folder structure as the original key
    const archivesBaseDir = path.resolve(__dirname, 'archives');
    const archivesDir = path.join(archivesBaseDir, dirPath);
    console.log(`archivesDir: ${archivesDir}`);

    // Ensure archives base directory exists
    if (!fs.existsSync(archivesBaseDir)) {
      await fsPromises.mkdir(archivesBaseDir, { recursive: true });
      console.log(`Created archives base directory: ${archivesBaseDir}`);
    }

    // Ensure the full directory path exists
    if (!fs.existsSync(archivesDir)) {
      await fsPromises.mkdir(archivesDir, { recursive: true });
      console.log(`Created directory structure: ${archivesDir}`);
    }

    // Check if it's a single DICOM file
    const fileStats = await fsPromises.stat(localInputPath);
    if (fileStats.isFile() && isDicomFile(filename)) {
      console.log(`Detected DICOM file: ${filename}, using DICOM compression`);

      // Use DICOM compression for individual DICOM files
      const compressedPath = await compressDicomFile(localInputPath, archivesDir);

      return {
        compressedFilePath: compressedPath,
        originalKey: originalKey
      };
    }

    // For directories or non-DICOM files, use regular ZIP archiving
    const outputPath = path.join(archivesDir, `${filename}.zmt`);
    console.log(`Compressing file/directory: ${localInputPath} to ${outputPath}`);

    // Create a file to stream archive data to
    const output = fs.createWriteStream(outputPath);
    const archive = archiver('zip', {
      zlib: { level: 9 } // Set the compression level (0-9)
    });

    // Set up warning and error handlers
    archive.on('warning', (err) => {
      if (err.code === 'ENOENT') {
        console.warn('Archive warning:', err);
      } else {
        throw err;
      }
    });

    // Listen for all archive data to be written
    const archiveFinished = new Promise((resolve, reject) => {
      output.on('close', () => {
        console.log(`Archive created: ${outputPath}, total bytes: ${archive.pointer()}`);
        resolve();
      });

      archive.on('error', (err) => {
        console.error('Archive error:', err);
        reject(err);
      });
    });

    // Pipe archive data to the file
    archive.pipe(output);

    // Add the file/directory to the archive
    try {
      if (fileStats.isDirectory()) {
        // If it's a directory, add the entire directory with the original path structure
        // We want to preserve the full path structure within the archive
        // Get the base directory name from the original key path
        const baseDir = path.basename(dirPath);

        // Use the directory name as the base in the archive
        // This preserves the nested structure while keeping the archive organized
        archive.directory(localInputPath, baseDir);
        console.log(`Adding directory: ${localInputPath} as ${baseDir}`);
      } else {
        // If it's a file, add the file with its original path structure
        // The name parameter specifies the path within the archive
        // For files, we just use the filename without any path
        archive.file(localInputPath, { name: filename });
        console.log(`Adding file: ${localInputPath} as ${filename}`);
      }

      // Finalize the archive (this is also a promise)
      await archive.finalize();

      // Wait for the archive to be fully written
      await archiveFinished;

      // Verify the archive was created and has content
      const archiveStats = await fsPromises.stat(outputPath);
      if (archiveStats.size === 0) {
        throw new Error('Created archive has zero size. Archiving may not be complete.');
      }

      // Return both the compressed file path and the original key to maintain the exact folder structure
      return {
        compressedFilePath: outputPath,
        originalKey: originalKey
      };
    } catch (err) {
      console.error(`Error adding files to archive: ${err.message}`);
      throw err;
    }
  } catch (error) {
    console.error(`Compression error for ${localInputPath}:`, error);
    throw error;
  }
}

module.exports = compressFile;
